import { FastifyRequest, FastifyReply } from 'fastify'
import { PrismaClient, Role } from '@prisma/client'
import { SSHService } from '../services/ssh'
import { CreateServerDTO, CommandResult, UpdateServerDTO } from '../types/server'

const prisma = new PrismaClient()

export async function listServers(request: FastifyRequest, reply: FastifyReply) {
  try {
    const servers = await prisma.server.findMany({
      where: {
        OR: [
          { userId: request.user.id },
          {
            userAccess: {
              some: {
                userId: request.user.id
              }
            }
          }
        ]
      },
      include: {
        commands: {
          orderBy: {
            order: 'asc'
          }
        },
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return reply.send({ servers })
  } catch (error) {
    console.error('Erro ao listar servidores:', error)
    return reply.status(500).send({ error: 'Erro interno do servidor' })
  }
}

export async function createServer(
  request: FastifyRequest<{ Body: CreateServerDTO }>,
  reply: FastifyReply,
) {
  try {
    // Verificar se o usuário é administrador
    if (request.user.role !== Role.ADMIN) {
      return reply.status(403).send({ error: 'Apenas administradores podem criar servidores' })
    }

    const { name, host, port, username, password, privateKey, deviceType, commands = [] } = request.body

    // Validar comandos se fornecidos
    if (commands.length > 0) {
      const invalidCommands = commands.filter(cmd => !cmd.name || !cmd.command)
      if (invalidCommands.length > 0) {
        return reply.status(400).send({ error: 'Todos os comandos fornecidos devem ter nome e comando' })
      }
    }

    const server = await prisma.server.create({
      data: {
        name,
        host,
        port,
        username,
        password,
        privateKey,
        deviceType: deviceType || 'GENERIC',
        userId: request.user.id,
        commands: commands.length > 0 ? {
          create: commands.map((cmd) => ({
            name: cmd.name,
            command: cmd.command,
            description: cmd.description || null,

          })),
        } : undefined,
      },
      include: {
        commands: true,
      },
    })

    return reply.status(201).send(server)
  } catch (error) {
    console.error('Erro ao criar servidor:', error)
    return reply.status(500).send({ error: 'Erro interno do servidor' })
  }
}

export async function updateServer(
  request: FastifyRequest<{ Params: { id: string }, Body: UpdateServerDTO }>,
  reply: FastifyReply,
) {
  try {
    const { id } = request.params
    const { name, host, port, username, password, privateKey, deviceType, commands = [] } = request.body



    // Validar comandos se fornecidos
    if (commands.length > 0) {
      const invalidCommands = commands.filter(cmd => !cmd.name || !cmd.command)
      if (invalidCommands.length > 0) {
        return reply.status(400).send({ error: 'Todos os comandos fornecidos devem ter nome e comando' })
      }
    }

    // Verificar se o servidor existe
    const server = await prisma.server.findUnique({
      where: { id },
      include: { commands: true },
    })

    if (!server) {
      return reply.status(404).send({ error: 'Servidor não encontrado' })
    }

    // Verificar se o usuário é o proprietário do servidor ou um administrador
    if (server.userId !== request.user.id && request.user.role !== Role.ADMIN) {
      return reply.status(403).send({ error: 'Apenas o proprietário ou administradores podem editar o servidor' })
    }

    // Obter os comandos existentes
    const existingCommands = await prisma.command.findMany({
      where: { serverId: id }
    })

    // Mapear os IDs dos comandos existentes por nome para facilitar a comparação
    const existingCommandMap = new Map()
    existingCommands.forEach(cmd => {
      existingCommandMap.set(cmd.name, cmd.id)
    })

    // Identificar comandos a serem atualizados, adicionados ou removidos
    const commandsToUpdate = []
    const commandsToCreate = []
    const commandNamesToKeep = new Set()

    // Processar os comandos recebidos na requisição
    for (const cmd of commands) {
      commandNamesToKeep.add(cmd.name)

      if (existingCommandMap.has(cmd.name)) {
        // Comando existente - atualizar
        commandsToUpdate.push({
          id: existingCommandMap.get(cmd.name),
          name: cmd.name,
          command: cmd.command,
          description: cmd.description || null,
          order: cmd.order !== undefined ? cmd.order : 0,
        })
      } else {
        // Novo comando - criar
        commandsToCreate.push({
          name: cmd.name,
          command: cmd.command,
          description: cmd.description || null,
          order: cmd.order !== undefined ? cmd.order : 0,
          serverId: id
        })
      }
    }

    // Identificar comandos a serem removidos
    // Comandos que existem no banco mas não estão na lista enviada pelo cliente
    // devem ser excluídos, pois foram removidos explicitamente na interface
    const commandsToRemove = existingCommands.filter(cmd => !commandNamesToKeep.has(cmd.name))

    // Log para depuração
    console.log(`Comandos a serem removidos: ${commandsToRemove.length}`,
      commandsToRemove.map(cmd => ({ id: cmd.id, name: cmd.name })))

    // Atualizar o servidor
    await prisma.server.update({
      where: { id },
      data: {
        name,
        host,
        port,
        username,
        password,
        privateKey,
        deviceType: deviceType || 'GENERIC',
      },
      include: {
        commands: true,
      },
    })

    // Atualizar comandos existentes
    for (const cmd of commandsToUpdate) {
      await prisma.command.update({
        where: { id: cmd.id },
        data: {
          name: cmd.name,
          command: cmd.command,
          description: cmd.description,
          order: cmd.order
        }
      })
    }

    // Criar novos comandos
    if (commandsToCreate.length > 0) {
      await prisma.command.createMany({
        data: commandsToCreate
      })
    }

    // Excluir comandos removidos
    if (commandsToRemove.length > 0) {
      console.log(`Excluindo ${commandsToRemove.length} comandos...`)
      await prisma.command.deleteMany({
        where: {
          id: {
            in: commandsToRemove.map(cmd => cmd.id)
          }
        }
      })
      console.log('Comandos excluídos com sucesso')
    }

    // Buscar o servidor atualizado com todos os comandos
    const refreshedServer = await prisma.server.findUnique({
      where: { id },
      include: {
        commands: true,
      },
    })



    // Nota: A ordenação dos comandos será feita automaticamente quando a migração for aplicada
    // Por enquanto, os comandos serão exibidos na ordem em que foram criados

    return reply.send(refreshedServer)
  } catch (error) {
    console.error('Erro ao atualizar servidor:', error)
    return reply.status(500).send({ error: 'Erro interno do servidor' })
  }
}

export async function deleteServer(
  request: FastifyRequest<{ Params: { id: string } }>,
  reply: FastifyReply,
) {
  try {
    const { id } = request.params

    // Verificar se o servidor existe
    const server = await prisma.server.findUnique({
      where: { id },
      include: { commands: true },
    })

    if (!server) {
      return reply.status(404).send({ error: 'Servidor não encontrado' })
    }

    // Verificar se o usuário é o proprietário do servidor
    if (server.userId !== request.user.id) {
      return reply.status(403).send({ error: 'Apenas o proprietário pode excluir o servidor' })
    }

    // Excluir o servidor e seus comandos (cascade)
    await prisma.server.delete({
      where: { id },
    })

    return reply.send({ message: 'Servidor excluído com sucesso' })
  } catch (error) {
    console.error('Erro ao excluir servidor:', error)
    return reply.status(500).send({ error: 'Erro interno do servidor' })
  }
}

// Mapa para controlar execuções simultâneas por servidor
const activeExecutions = new Map<string, boolean>();

export async function searchCommands(
  request: FastifyRequest<{ Querystring: { q: string } }>,
  reply: FastifyReply,
) {
  try {
    const { q } = request.query

    if (!q || q.trim().length < 2) {
      return reply.send({ commands: [] })
    }

    const searchTerm = q.trim().toLowerCase()

    // Buscar comandos que contenham o termo de pesquisa
    const commands = await prisma.command.findMany({
      where: {
        AND: [
          {
            OR: [
              { name: { contains: searchTerm, mode: 'insensitive' } },
              { description: { contains: searchTerm, mode: 'insensitive' } },
              { command: { contains: searchTerm, mode: 'insensitive' } }
            ]
          },
          { isLatest: true } // Apenas comandos da versão mais recente
        ]
      },
      include: {
        server: {
          select: {
            id: true,
            name: true,
            host: true,
            deviceType: true
          }
        }
      },
      orderBy: [
        { name: 'asc' },
        { server: { name: 'asc' } }
      ],
      take: 20 // Limitar a 20 resultados para performance
    })

    // Verificar acesso do usuário aos servidores
    const userServerAccess = await prisma.serverUser.findMany({
      where: { userId: request.user.id },
      select: { serverId: true }
    })

    const accessibleServerIds = new Set(userServerAccess.map(access => access.serverId))

    // Filtrar apenas comandos de servidores que o usuário tem acesso
    const accessibleCommands = commands.filter(command =>
      accessibleServerIds.has(command.serverId)
    )

    // Formatar resposta
    const formattedCommands = accessibleCommands.map(command => ({
      id: command.id,
      name: command.name,
      description: command.description,
      command: command.command,
      serverId: command.serverId,
      serverName: command.server.name,
      serverHost: command.server.host,
      deviceType: command.server.deviceType,
      displayText: `${command.name} - ${command.server.name}`
    }))

    return reply.send({ commands: formattedCommands })
  } catch (error) {
    console.error('Erro ao buscar comandos:', error)
    return reply.status(500).send({ error: 'Erro ao buscar comandos' })
  }
}

export async function executeCommand(
  request: FastifyRequest<{ Params: { id: string }, Body: { commandId: string } }>,
  reply: FastifyReply,
) {
  try {
    const { id } = request.params
    const { commandId } = request.body

    // Verificar se já existe uma execução em andamento para este servidor
    if (activeExecutions.get(id)) {
      return reply.status(409).send({
        error: 'Comando em execução',
        message: 'Já existe um comando em execução para este servidor. Aguarde a conclusão antes de enviar um novo comando.'
      })
    }

    // Marcar servidor como em uso
    activeExecutions.set(id, true)

    // Buscar servidor e comando no banco
    const server = await prisma.server.findUnique({
      where: { id },
      include: { commands: true },
    })

    if (!server) {
      // Liberar o servidor
      activeExecutions.set(id, false)
      return reply.status(404).send({ error: 'Servidor não encontrado' })
    }

    // Verificar se o usuário é o proprietário do servidor
    const isOwner = server.userId === request.user.id

    // Se não for o proprietário, verificar se tem acesso ao servidor
    if (!isOwner) {
      const access = await prisma.$queryRaw`
        SELECT * FROM "ServerUser"
        WHERE "serverId" = ${id} AND "userId" = ${request.user.id}
        LIMIT 1
      `

      // Se o array estiver vazio, o usuário não tem acesso
      if (!access || (Array.isArray(access) && access.length === 0)) {
        // Liberar o servidor
        activeExecutions.set(id, false)
        return reply.status(403).send({ error: 'Acesso negado' })
      }
    }

    const command = server.commands.find(c => c.id === commandId)
    if (!command) {
      // Liberar o servidor
      activeExecutions.set(id, false)
      return reply.status(404).send({ error: 'Comando não encontrado' })
    }

    // Criar uma instância do serviço SSH com tratamento de erros melhorado
    const sshService = new SSHService()

    // Converter o objeto do Prisma para o formato esperado pelo SSHService
    const sshServer = {
      id: server.id,
      name: server.name,
      host: server.host,
      port: server.port,
      username: server.username,
      password: server.password || undefined,
      privateKey: server.privateKey || undefined,
      os: server.os,
      deviceType: server.deviceType,
    }

    // Variável para armazenar o resultado do comando
    let result: CommandResult = { stdout: '', stderr: '', code: 0 };

    try {
      // Calcular timeout dinâmico com base na complexidade do comando
      const commandText = command.command;
      const commandLines = commandText.split('\n').filter(line => line.trim() !== '');
      const commandCount = commandLines.length;
      const commandComplexity = Math.max(
        commandCount,
        commandText.split(';').length + commandText.split('|').length
      );

      // Calcular timeout dinâmico (usando a mesma lógica dos executores)
      const BASE_TIMEOUT = 30000; // 30 segundos como base
      const TIMEOUT_PER_COMMAND = 5000; // 5 segundos adicionais por comando
      const MAX_TIMEOUT = 300000; // Limite máximo de 5 minutos

      const dynamicTimeout = Math.min(BASE_TIMEOUT + (commandComplexity * TIMEOUT_PER_COMMAND), MAX_TIMEOUT);

      console.log(`Usando timeout dinâmico de ${dynamicTimeout}ms para comando com complexidade ${commandComplexity}`);

      // Definir um timeout global para a operação completa
      const timeoutPromise = new Promise<CommandResult>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Timeout global atingido após ${dynamicTimeout/1000} segundos ao executar comando`))
        }, dynamicTimeout)
      })

      // Executar o comando com timeout
      const executePromise = async (): Promise<CommandResult> => {
        await sshService.connect(sshServer)
        const cmdResult = await sshService.executeCommand(command.command)
        return cmdResult
      }

      // Usar Promise.race para implementar o timeout
      result = await Promise.race([executePromise(), timeoutPromise])
    } catch (error) {
      console.error(`Erro ao executar comando no servidor ${server.name}:`, error)

      // Criar um resultado de erro
      result = {
        stdout: '',
        stderr: `Erro ao executar comando: ${error instanceof Error ? error.message : 'Erro desconhecido'}`,
        code: 1
      }
    } finally {
      // Garantir que a conexão seja sempre fechada, independentemente do resultado
      try {
        console.log(`Desconectando sessão SSH para o servidor ${server.name} após execução do comando`)
        await sshService.disconnect()
      } catch (disconnectError) {
        console.error('Erro ao desconectar SSH:', disconnectError)
      } finally {
        // Liberar o servidor para novas execuções
        activeExecutions.set(id, false)
      }
    }

    // Registrar o histórico do comando - sempre executado, independente de sucesso ou erro
    try {
      // Registrar o histórico do comando com campos adicionais para compatibilidade
      await prisma.commandHistory.create({
        data: {
          userId: request.user.id,
          serverId: server.id,
          commandId: command.id,
          commandName: command.name,
          commandText: command.command,
          result: JSON.stringify(result),
          status: result.code || 0
        }
      });

      console.log(`Histórico de comando registrado com sucesso: ${command.id}`);
    } catch (historyError) {
      console.error('Erro ao registrar histórico de comando:', historyError)
      // Não interromper o fluxo se falhar ao registrar o histórico
    }

    // Retornar o resultado para o cliente
    return reply.send(result)
  } catch (error) {
    console.error('Erro ao processar requisição de comando:', error)

    // Garantir que o servidor seja liberado em caso de erro não tratado
    if (request.params && request.params.id) {
      activeExecutions.set(request.params.id, false)
    }

    return reply.status(500).send({
      error: 'Erro ao processar comando',
      message: error instanceof Error ? error.message : 'Erro desconhecido'
    })
  }
}

// Função para listar o histórico de comandos (acessível para todos os usuários)
// Atualizar a ordem dos comandos
export async function updateCommandsOrder(
  request: FastifyRequest<{
    Params: { id: string },
    Body: { commands: { id: string, order: number }[] }
  }>,
  reply: FastifyReply,
) {
  try {
    const { id } = request.params;
    const { commands } = request.body;

    // Verificar se o servidor existe
    const server = await prisma.server.findUnique({
      where: { id },
      include: { commands: true },
    });

    if (!server) {
      return reply.status(404).send({ error: 'Servidor não encontrado' });
    }

    // Verificar se o usuário é o proprietário do servidor ou um administrador
    if (server.userId !== request.user.id && request.user.role !== Role.ADMIN) {
      return reply.status(403).send({ error: 'Apenas o proprietário ou administradores podem editar o servidor' });
    }

    // Atualizar a ordem de cada comando
    for (const cmd of commands) {
      await prisma.command.update({
        where: { id: cmd.id },
        data: { order: cmd.order }
      });
    }

    // Buscar o servidor atualizado com todos os comandos
    const refreshedServer = await prisma.server.findUnique({
      where: { id },
      include: {
        commands: {
          orderBy: {
            order: 'asc',
          },
        },
      },
    });

    return reply.send(refreshedServer);
  } catch (error) {
    console.error('Erro ao atualizar ordem dos comandos:', error);
    return reply.status(500).send({ error: 'Erro interno do servidor' });
  }
}

export async function listCommandHistory(
  request: FastifyRequest<{
    Querystring: {
      page?: number;
      limit?: number;
    }
  }>,
  reply: FastifyReply,
) {
  try {
    const page = Number(request.query.page) || 1;
    const limit = Number(request.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Buscar o histórico de comandos com suporte a commandId nullable
    const historyQuery = prisma.commandHistory.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            active: true,
          },
        },
        server: {
          select: {
            id: true,
            name: true,
            host: true,
          },
        },
        command: {
          select: {
            id: true,
            name: true,
            command: true,
          },
        },
      },
      orderBy: {
        executedAt: 'desc',
      },
      skip,
      take: limit,
    });

    // Contar o total de registros
    const totalQuery = prisma.commandHistory.count();

    // Executar as consultas em paralelo
    const [total, historyRaw] = await Promise.all([totalQuery, historyQuery]);

    // Processar os resultados para lidar com comandos excluídos
    let history = [];

    // Processar os resultados brutos se estivermos usando a consulta SQL bruta
    if (Array.isArray(historyRaw) && historyRaw.length > 0 && 'userName' in historyRaw[0]) {
      // Estamos usando a consulta SQL bruta, precisamos formatar os dados
      history = historyRaw.map((row: any) => ({
        id: row.id,
        userId: row.userId,
        serverId: row.serverId,
        commandId: row.commandId,
        commandName: row.commandName,
        commandText: row.commandCommand,
        result: row.result,
        status: row.status,
        executedAt: row.executedAt,
        createdAt: row.createdAt,
        user: {
          id: row.userId,
          name: row.userName,
          email: row.userEmail,
          active: row.userActive
        },
        server: {
          id: row.serverId,
          name: row.serverName,
          host: row.serverHost
        },
        command: row.commandId ? {
          id: row.commandId,
          name: row.commandName,
          command: row.commandCommand
        } : null
      }));
    } else {
      // Estamos usando a consulta normal do Prisma
      history = historyRaw as any[];
    }

    // Processar os resultados para lidar com comandos excluídos
    const processedHistory = history.map((item: any) => {
      // Se o comando foi excluído mas temos os dados armazenados no histórico
      if (!item.command && (item.commandName || item.commandText)) {
        return {
          ...item,
          command: {
            id: 'deleted',
            name: item.commandName || 'Comando excluído',
            command: item.commandText || 'Comando não disponível'
          }
        };
      }
      return item;
    });

    const totalPages = Math.ceil(total / limit);

    return reply.send({
      history: processedHistory,
      pagination: {
        total,
        totalPages,
        currentPage: page,
        limit,
      }
    });
  } catch (error) {
    console.error('Erro ao listar histórico de comandos:', error);
    return reply.status(500).send({ error: 'Erro ao listar histórico de comandos' });
  }
}